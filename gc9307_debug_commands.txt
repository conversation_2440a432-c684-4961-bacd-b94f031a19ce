# GC9307 调试命令集合
# 在 U-Boot 命令行中执行这些命令来启用调试

# 1. 基本调试启用命令
echo "=== 启用 GC9307 调试 ==="

# 设置全局日志级别为调试级别
setenv loglevel 7
log level 7

# 启用视频子系统调试
log level video debug
log level video 7

# 启用 SPI 调试
log level spi debug

# 启用 GPIO 调试
log level gpio debug

# 启用设备模型调试
log level dm debug

# 2. 查看当前配置命令
echo "=== 查看当前调试配置 ==="

# 显示日志配置
log rec

# 显示环境变量
printenv loglevel

# 显示设备信息
dm tree
dm uclass video

# 3. 测试显示设备命令
echo "=== 测试显示设备 ==="

# 查看视频设备
video list

# 显示测试图案（如果支持）
# bmp display <address>

# 4. GPIO 状态检查命令
echo "=== 检查 GPIO 状态 ==="

# 显示所有 GPIO 状态
gpio status

# 5. SPI 设备检查命令  
echo "=== 检查 SPI 设备 ==="

# 扫描 SPI 设备
# spi probe

# 6. 设备树检查命令
echo "=== 检查设备树配置 ==="

# 显示显示相关的设备树节点
fdt list /soc
# fdt list /soc/spi@20000000
# fdt list /soc/spi@20000000/gc9307@0

# 7. 内存和缓冲区检查命令
echo "=== 内存和缓冲区检查 ==="

# 显示内存映射
bdinfo

# 检查视频内存
# md.l <framebuffer_address> 0x100

# 8. 实时日志监控命令
echo "=== 实时日志监控 ==="

# 显示最近的日志记录
log rec -l debug

# 显示视频相关日志
log rec -c video

# 显示 SPI 相关日志  
log rec -c spi

# 9. 性能监控命令
echo "=== 性能监控 ==="

# 启用时间戳
log format-add timestamp

# 10. 清理和重置命令
echo "=== 清理和重置 ==="

# 清除日志缓冲区
# log clear

# 重置日志级别（如果需要）
# log level 4

# 11. 保存调试配置
echo "=== 保存调试配置 ==="

# 保存环境变量
saveenv

# 12. 常用调试序列
echo "=== 常用调试序列 ==="

# 完整的调试启用序列
setenv debug_gc9307 'setenv loglevel 7; log level video debug; log level spi debug; log level gpio debug'

# 快速状态检查序列  
setenv check_gc9307 'dm tree; gpio status; log rec -c video'

# 显示测试序列
setenv test_gc9307 'video list; log rec -l debug'

echo "=== 调试命令设置完成 ==="
echo "使用 'run debug_gc9307' 启用调试"
echo "使用 'run check_gc9307' 检查状态"
echo "使用 'run test_gc9307' 进行测试"
