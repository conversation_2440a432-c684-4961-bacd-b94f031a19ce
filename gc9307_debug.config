# GC9307 调试配置文件
# 将这些配置添加到您的 defconfig 文件中以启用完整的调试支持

# 基本日志支持
CONFIG_LOG=y
CONFIG_LOG_MAX_LEVEL=7
CONFIG_LOG_DEFAULT_LEVEL=6
CONFIG_LOG_ERROR_RETURN=y

# SPL 日志支持
CONFIG_SPL_LOG=y
CONFIG_SPL_LOG_MAX_LEVEL=7

# 调试 UART 支持
CONFIG_DEBUG_UART=y
CONFIG_DEBUG_UART_BASE=0x02800000
CONFIG_DEBUG_UART_CLOCK=48000000
CONFIG_DEBUG_UART_SHIFT=2

# 视频调试支持
CONFIG_VIDEO_LOGO=n
CONFIG_SYS_WHITE_ON_BLACK=y

# SPI 调试支持
CONFIG_DEBUG_SPI=y

# GPIO 调试支持  
CONFIG_DEBUG_GPIO=y

# 设备模型调试
CONFIG_DM_DEBUG=y

# 额外的调试选项（可选）
CONFIG_DEBUG_UART_ANNOUNCE=y
CONFIG_DEBUG_UART_SKIP_INIT=n

# 内存调试（如果需要）
# CONFIG_DEBUG_MALLOC=y

# 命令行调试工具
CONFIG_CMD_LOG=y
CONFIG_CMD_TRACE=y
