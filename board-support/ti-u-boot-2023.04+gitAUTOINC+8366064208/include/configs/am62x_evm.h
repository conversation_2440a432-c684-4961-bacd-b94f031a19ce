/* SPDX-License-Identifier: GPL-2.0+ */
/*
 * Configuration header file for K3 AM625 SoC family
 *
 * Copyright (C) 2020-2022 Texas Instruments Incorporated - https://www.ti.com/
 *	<PERSON><PERSON> <<EMAIL>>
 */

#ifndef __CONFIG_AM625_EVM_H
#define __CONFIG_AM625_EVM_H

#include <config_distro_bootcmd.h>

/* DDR Configuration */
#define CFG_SYS_SDRAM_BASE1             0x880000000

#define CONFIG_UBOOT_LOG 				1

/* Now for the remaining common defines */
#include <configs/ti_armv7_common.h>

/* include Android related settings */
#if CONFIG_CMD_ABOOTIMG
#include <configs/am62x_evm_android.h>
#endif

/* NAND Driver config */
#define CFG_SYS_NAND_BASE            0x51000000

#define CFG_SYS_NAND_ECCPOS		{ 2, 3, 4, 5, 6, 7, 8, 9, \
					 10, 11, 12, 13, 14, 15, 16, 17, \
					 18, 19, 20, 21, 22, 23, 24, 25, \
					 26, 27, 28, 29, 30, 31, 32, 33, \
					 34, 35, 36, 37, 38, 39, 40, 41, \
					 42, 43, 44, 45, 46, 47, 48, 49, \
					 50, 51, 52, 53, 54, 55, 56, 57, }

#define CFG_SYS_NAND_ECCSIZE         512

#define CFG_SYS_NAND_ECCBYTES        14
/*-- end NAND config --*/

#endif /* __CONFIG_AM625_EVM_H */
