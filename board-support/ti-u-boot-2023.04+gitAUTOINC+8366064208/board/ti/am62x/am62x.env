#include <environment/ti/ti_armv7_common.env>
#if CONFIG_CMD_REMOTEPROC
#include <environment/ti/k3_rproc.env>
#endif
baudrate=1500000
fdt_high=0xffffffffffffffff
default_device_tree=ti/k3-am62-lp-sk.dtb
findfdt=
	setenv name_fdt ${default_device_tree};
	if test $board_name = am62x_skevm; then
			setenv name_fdt ti/k3-am625-sk.dtb; fi;
	if test $board_name = am62x_lp_skevm; then
			setenv name_fdt ti/k3-am62-lp-sk.dtb; fi;
	if test $board_name = am62x_beagleplay; then
			setenv name_fdt ti/k3-am625-beagleplay.dtb; fi;
	setenv fdtfile ${name_fdt}
name_kern=Image
console=ttyS2,1500000n8
args_all=setenv optargs ${optargs} earlycon=ns16550a,mmio32,0x02800000,1500000 ${mtdparts}
mtdparts_set=setenv mtdparts  fc40000.spi.0:512k(ospi.tiboot3),1m(ospi.tispl),1m(ospi.u-boot),128k(ospi.dtb),128k(ospi.prod),512k(ospi.calibration),60m@8m(ospi.kernel),21m(rootfs),31m(victel),256k(ospi.phypattern)
run_kern=booti ${loadaddr} ${rd_spec} ${fdtaddr} 
rd_spec=-

optargs=earlycon=ns16550a,mmio32,0x02800000
bootargs_set=setenv bootargs console=${console} ${optargs} root=/dev/mtdblock7 rootfstype=squashfs ro ubi.mtd=8 noinitrd mem=256M init=/linuxrc printk.time=1 initcall_debug=1
bootak811=run mtdparts_set;run args_all; run bootargs_set;sf probe; sf read ${loadaddr} 0x300000 0x800000; sf read ${fdtaddr} 0x280000 0x10000;setenv kernel_comp_addr_r 0x84000000;setenv kernel_comp_size 0x2000000; run run_kern;
rproc_fw_binaries= 0 /lib/firmware/am62-mcu-m4f0_0-fw
sdflash=mmc dev 1;mmc rescan;sf probe;sf format;load mmc 1:1 ${loadaddr} tiboot3_qspi.bin;sf update $loadaddr 0x0 $filesize;load mmc 1:1 ${loadaddr} tispl_qspi.bin;sf update $loadaddr 0x80000 $filesize;load mmc 1:1 ${loadaddr} u-boot_qspi.bin;sf update $loadaddr 0x180000 $filesize;load mmc 1:1 ${loadaddr} p3.dtb;sf update ${loadaddr} 0x280000 ${filesize};load mmc 1:1 ${loadaddr} os.bin;sf update ${loadaddr} 0x300000 ${filesize};load mmc 1:1 ${loadaddr} rootfs.img;sf update ${loadaddr} 0xb00000 ${filesize};load mmc 1:1 ${loadaddr} victel.img;sf update ${loadaddr} 0x2000000 ${filesize};run bootak811;