// SPDX-License-Identifier: GPL-2.0+
/*
 * GC9307 SPI LCD driver for U-Boot
 * Based on Linux kernel gc9307.c driver
 *
 * Copyright (C) 2025 Victel
 * Author: cfl
 */

/* Enable debug output for this driver */
#define DEBUG
#define LOG_DEBUG 1

#include <common.h>
#include <dm.h>
#include <spi.h>
#include <video.h>
#include <asm/gpio.h>
#include <linux/delay.h>
#include <malloc.h>
#include <dm/device.h>
#include <dm/uclass.h>
#include <dm/read.h>
#include <dm/device_compat.h>
#include <log.h>

#define LCD_WIDTH   320
#define LCD_HEIGHT  240

/* GC9307 register commands */
#define GC9307_SWRESET      0x01
#define GC9307_SLPOUT       0x11
#define GC9307_DISPON       0x29
#define GC9307_CASET        0x2A
#define GC9307_RASET        0x2B
#define GC9307_RAMWR        0x2C
#define GC9307_MADCTL       0x36
#define GC9307_COLMOD       0x3A

struct gc9307_priv {
    struct udevice *spi_dev;
    struct gpio_desc reset_gpio;
    struct gpio_desc dc_gpio;
    struct gpio_desc cs_gpio;
    struct gpio_desc led_gpio;
    u32 width;
    u32 height;
    u32 rotate;
};

static int gc9307_spi_write_cmd(struct udevice *dev, u8 cmd)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    printf("GC9307: Writing command 0x%02x\n", cmd);

    /* Set DC low for command */
    ret = dm_gpio_set_value(&priv->dc_gpio, 0);
    if (ret) {
        printf( "Failed to set DC GPIO low for command 0x%02x: %d\n", cmd, ret);
        return ret;
    }

    ret = dm_spi_xfer(priv->spi_dev, 8, &cmd, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        printf( "SPI command 0x%02x transfer failed: %d\n", cmd, ret);
    } else {
        printf("GC9307: Command 0x%02x sent successfully\n", cmd);
    }

    return ret;
}

static int gc9307_spi_write_data(struct udevice *dev, u8 data)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    printf("GC9307: Writing data 0x%02x\n", data);

    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret) {
        printf( "Failed to set DC GPIO high for data 0x%02x: %d\n", data, ret);
        return ret;
    }

    ret = dm_spi_xfer(priv->spi_dev, 8, &data, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        printf( "SPI data 0x%02x transfer failed: %d\n", data, ret);
    } else {
        printf("GC9307: Data 0x%02x sent successfully\n", data);
    }

    return ret;
}

static int gc9307_spi_write_data_buf(struct udevice *dev, const u8 *buf, size_t len)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    printf("GC9307: Writing data buffer, length: %zu bytes\n", len);

    /* Set DC high for data */
    ret = dm_gpio_set_value(&priv->dc_gpio, 1);
    if (ret) {
        printf( "Failed to set DC GPIO high for data buffer: %d\n", ret);
        return ret;
    }

    ret = dm_spi_xfer(priv->spi_dev, len * 8, buf, NULL, SPI_XFER_BEGIN | SPI_XFER_END);
    if (ret) {
        printf( "SPI data buffer transfer failed (len=%zu): %d\n", len, ret);
    } else {
        printf("GC9307: Data buffer (%zu bytes) sent successfully\n", len);
    }

    return ret;
}

static int gc9307_set_address_window(struct udevice *dev, u16 x1, u16 y1, u16 x2, u16 y2)
{
    int ret;

    printf("GC9307: Setting address window: x1=%d, y1=%d, x2=%d, y2=%d\n", x1, y1, x2, y2);

    /* Column address set */
    ret = gc9307_spi_write_cmd(dev, GC9307_CASET);
    if (ret) {
        printf( "Failed to send CASET command: %d\n", ret);
        return ret;
    }
    ret = gc9307_spi_write_data(dev, x1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, x2 & 0xFF);
    if (ret) return ret;

    /* Row address set */
    ret = gc9307_spi_write_cmd(dev, GC9307_RASET);
    if (ret) {
        printf( "Failed to send RASET command: %d\n", ret);
        return ret;
    }
    ret = gc9307_spi_write_data(dev, y1 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y1 & 0xFF);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 >> 8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, y2 & 0xFF);
    if (ret) return ret;

    printf("GC9307: Address window set successfully\n");
    return 0;
}

static int gc9307_clear_screen(struct udevice *dev)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    u8 *buffer;
    size_t buffer_size = priv->width * priv->height * 2; /* 16-bit color */
    int ret;
    
    buffer = malloc(buffer_size);
    if (!buffer)
        return -ENOMEM;
        
    /* Fill with black (0x0000) */
    memset(buffer, 0x00, buffer_size);
    
    ret = gc9307_set_address_window(dev, 0, 0, priv->width - 1, priv->height - 1);
    if (ret)
        goto cleanup;
        
    ret = gc9307_spi_write_cmd(dev, GC9307_RAMWR);
    if (ret)
        goto cleanup;
        
    ret = gc9307_spi_write_data_buf(dev, buffer, buffer_size);
    
cleanup:
    free(buffer);
    return ret;
}

static int gc9307_init_display(struct udevice *dev)
{
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    printf("GC9307: Starting display initialization\n");

    /* Hardware reset */
    printf("GC9307: Performing hardware reset sequence\n");
    ret = dm_gpio_set_value(&priv->reset_gpio, 1);
    if (ret) {
        printf( "Failed to set reset GPIO high: %d\n", ret);
        return ret;
    }
    mdelay(50);

    ret = dm_gpio_set_value(&priv->reset_gpio, 0);
    if (ret) {
        printf( "Failed to set reset GPIO low: %d\n", ret);
        return ret;
    }
    mdelay(50);

    ret = dm_gpio_set_value(&priv->reset_gpio, 1);
    if (ret) {
        printf( "Failed to set reset GPIO high (final): %d\n", ret);
        return ret;
    }
    mdelay(120);
    printf("GC9307: Hardware reset completed\n");

    /* Software reset */
    printf("GC9307: Sending software reset command\n");
    ret = gc9307_spi_write_cmd(dev, GC9307_SWRESET);
    if (ret) {
        printf( "Failed to send software reset: %d\n", ret);
        return ret;
    }
    mdelay(120);

    /* Sleep out */
    printf("GC9307: Sending sleep out command\n");
    ret = gc9307_spi_write_cmd(dev, GC9307_SLPOUT);
    if (ret) {
        printf( "Failed to send sleep out: %d\n", ret);
        return ret;
    }
    mdelay(120);

    /* Initial commands */
    printf("GC9307: Sending initial commands\n");
    ret = gc9307_spi_write_cmd(dev, 0xfe);
    if (ret) return ret;
    ret = gc9307_spi_write_cmd(dev, 0xef);
    if (ret) return ret;

    /* Memory access control */
    printf("GC9307: Setting memory access control (rotation=%d)\n", priv->rotate);
    ret = gc9307_spi_write_cmd(dev, GC9307_MADCTL);
    if (ret) return ret;
    if (priv->rotate == 0) {
        ret = gc9307_spi_write_data(dev, 0xe8);
    } else if (priv->rotate == 180) {
        ret = gc9307_spi_write_data(dev, 0x38);
    } else {
        ret = gc9307_spi_write_data(dev, 0xe8); /* default 0 degrees */
    }
    if (ret) return ret;

    /* Interface pixel format - 16-bit color */
    printf("GC9307: Setting pixel format to 16-bit\n");
    ret = gc9307_spi_write_cmd(dev, GC9307_COLMOD);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x05);
    if (ret) return ret;

    printf("GC9307: Display initialization completed successfully\n");
    return 0;
}

static int gc9307_init_gamma_settings(struct udevice *dev)
{
    int ret;

    /* Power control settings */
    ret = gc9307_spi_write_cmd(dev, 0x86);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x98);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x89);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x03);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x80);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x8e);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0f);
    if (ret) return ret;

    /* Frame rate control */
    ret = gc9307_spi_write_cmd(dev, 0xe8);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x12);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* Power control settings */
    ret = gc9307_spi_write_cmd(dev, 0xc3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x47);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc4);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0xc9);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x00);
    if (ret) return ret;

    /* Extended command set */
    ret = gc9307_spi_write_cmd(dev, 0xff);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x62);
    if (ret) return ret;

    /* Display enhancement */
    ret = gc9307_spi_write_cmd(dev, 0x99);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x3e);
    if (ret) return ret;

    ret = gc9307_spi_write_cmd(dev, 0x9d);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4b);
    if (ret) return ret;

    return 0;
}

static int gc9307_init_gamma_correction(struct udevice *dev)
{
    int ret;

    /* Positive Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF0);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0b);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0c);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x0a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x31);
    if (ret) return ret;

    /* Negative Gamma Control */
    ret = gc9307_spi_write_cmd(dev, 0xF2);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x07);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x04);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x06);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x21);
    if (ret) return ret;

    /* Positive Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF1);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x4a);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x78);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x76);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x33);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x2f);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0xaf);
    if (ret) return ret;

    /* Negative Gamma Correction */
    ret = gc9307_spi_write_cmd(dev, 0xF3);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x38);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x74);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x72);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x22);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x28);
    if (ret) return ret;
    ret = gc9307_spi_write_data(dev, 0x6f);
    if (ret) return ret;

    /* Tearing Effect Line Off */
    ret = gc9307_spi_write_cmd(dev, 0x34);
    if (ret) return ret;

    return 0;
}

static int gc9307_video_sync(struct udevice *vid)
{
    struct video_priv *uc_priv = dev_get_uclass_priv(vid);
    struct gc9307_priv *priv = dev_get_priv(vid);
    u8 *fb = uc_priv->fb;
    int ret;

    printf("GC9307: Starting video sync\n");

    if (!fb) {
        printf("Framebuffer is NULL\n");
        return -EINVAL;
    }

    printf("GC9307: Framebuffer size: %d bytes\n", uc_priv->fb_size);

    ret = dm_spi_claim_bus(priv->spi_dev);
    if (ret) {
        printf("Failed to claim SPI bus: %d\n", ret);
        return ret;
    }
    printf("GC9307: SPI bus claimed\n");

    /* Set address window to full screen */
    ret = gc9307_set_address_window(vid, 0, 0, priv->width - 1, priv->height - 1);
    if (ret) {
        printf("Failed to set address window: %d\n", ret);
        goto release_bus;
    }

    /* Start memory write */
    printf("GC9307: Starting memory write\n");
    ret = gc9307_spi_write_cmd(vid, GC9307_RAMWR);
    if (ret) {
        printf("Failed to send RAMWR command: %d\n", ret);
        goto release_bus;
    }

    /* Send framebuffer data */
    printf("GC9307: Sending framebuffer data (%d bytes)\n", uc_priv->fb_size);
    ret = gc9307_spi_write_data_buf(vid, fb, uc_priv->fb_size);
    if (ret) {
        printf("Failed to send framebuffer data: %d\n", ret);
    } else {
        printf("GC9307: Video sync completed successfully\n");
    }

release_bus:
    dm_spi_release_bus(priv->spi_dev);
    printf("GC9307: SPI bus released\n");
    return ret;
}

static int gc9307_probe(struct udevice *dev)
{
    struct video_priv *uc_priv = dev_get_uclass_priv(dev);
    struct gc9307_priv *priv = dev_get_priv(dev);
    int ret;

    printf("GC9307: Starting probe function\n");

    /* Get SPI device */
    priv->spi_dev = dev;
    printf("GC9307: SPI device assigned\n");

    /* Get GPIO descriptors */
    printf("GC9307: Requesting GPIO pins\n");
    ret = gpio_request_by_name(dev, "reset-gpios", 0, &priv->reset_gpio, GPIOD_IS_OUT);
    if (ret) {
        printf( "Failed to get reset GPIO: %d\n", ret);
        return ret;
    }
    printf("GC9307: Reset GPIO acquired\n");

    ret = gpio_request_by_name(dev, "dc-gpios", 0, &priv->dc_gpio, GPIOD_IS_OUT);
    if (ret) {
        printf( "Failed to get DC GPIO: %d\n", ret);
        return ret;
    }
    printf("GC9307: DC GPIO acquired\n");

    ret = gpio_request_by_name(dev, "cs-gpios", 0, &priv->cs_gpio, GPIOD_IS_OUT);
    if (ret) {
        printf( "Failed to get CS GPIO: %d\n", ret);
        return ret;
    }
    printf("GC9307: CS GPIO acquired\n");

    ret = gpio_request_by_name(dev, "led-gpios", 0, &priv->led_gpio, GPIOD_IS_OUT);
    if (ret) {
        printf( "Failed to get LED GPIO: %d\n", ret);
        return ret;
    }
    printf("GC9307: LED GPIO acquired\n");

    /* Get display properties from device tree */
    priv->width = dev_read_u32_default(dev, "width", LCD_WIDTH);
    priv->height = dev_read_u32_default(dev, "height", LCD_HEIGHT);
    priv->rotate = dev_read_u32_default(dev, "rotate", 0);
    printf("GC9307: Display properties: %dx%d, rotation=%d\n",
          priv->width, priv->height, priv->rotate);

    /* Set up video properties */
    uc_priv->bpix = VIDEO_BPP16;
    uc_priv->xsize = priv->width;
    uc_priv->ysize = priv->height;
    uc_priv->rot = 0;
    printf("GC9307: Video properties configured\n");

    /* Enable backlight */
    printf("GC9307: Enabling backlight\n");
    ret = dm_gpio_set_value(&priv->led_gpio, 1);
    if (ret) {
        printf( "Failed to enable backlight: %d\n", ret);
        return ret;
    }
    printf("GC9307: Backlight enabled\n");

    /* Initialize display */
    printf("GC9307: Starting display initialization sequence\n");
    ret = gc9307_init_display(dev);
    if (ret) {
        printf( "Failed to initialize display: %d\n", ret);
        return ret;
    }

    printf("GC9307: Initializing gamma settings\n");
    ret = gc9307_init_gamma_settings(dev);
    if (ret) {
        printf( "Failed to initialize gamma settings: %d\n", ret);
        return ret;
    }

    printf("GC9307: Initializing gamma correction\n");
    ret = gc9307_init_gamma_correction(dev);
    if (ret) {
        printf( "Failed to initialize gamma correction: %d\n", ret);
        return ret;
    }

    /* Clear screen */
    printf("GC9307: Clearing screen\n");
    ret = gc9307_clear_screen(dev);
    if (ret) {
        printf( "Failed to clear screen: %d\n", ret);
        return ret;
    }

    /* Display on */
    printf("GC9307: Turning on display\n");
    ret = gc9307_spi_write_cmd(dev, GC9307_DISPON);
    if (ret) {
        printf( "Failed to turn on display: %d\n", ret);
        return ret;
    }

    printf("GC9307 display initialized successfully (%dx%d)\n", priv->width, priv->height);
    printf("GC9307: Probe completed successfully\n");

    return 0;
}

static int gc9307_bind(struct udevice *dev)
{
    struct video_uc_plat *plat = dev_get_uclass_plat(dev);
    u32 width, height;

    /* Get display size from device tree */
    width = dev_read_u32_default(dev, "width", LCD_WIDTH);
    height = dev_read_u32_default(dev, "height", LCD_HEIGHT);

    /* Set framebuffer size (16-bit color) */
    plat->size = width * height * 2;

    return 0;
}

static const struct video_ops gc9307_ops = {
    .video_sync = gc9307_video_sync,
};

static const struct udevice_id gc9307_ids[] = {
    { .compatible = "victel,gc9307-uboot" },
    { }
};

U_BOOT_DRIVER(gc9307_video) = {
    .name = "gc9307_video",
    .id = UCLASS_VIDEO,
    .of_match = gc9307_ids,
    .ops = &gc9307_ops,
    .bind = gc9307_bind,
    .probe = gc9307_probe,
    .priv_auto = sizeof(struct gc9307_priv),
    .flags = DM_FLAG_PRE_RELOC,
};
