/*
 * AK811/809 Radio Platform
 *
 * Copyright (C) Victel Incorporated
 */


/dts-v1/;

#include <dt-bindings/leds/common.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "k3-am625.dtsi"
#include "p3-v4.dtsi"
/ {
    compatible = "victel, P3V4.0", "ti,am625";
    model = "VICTEL TWOWAY RADIO AM62x PLATFORM";

    cpus {
        cpu-map {
            cluster0 {
                /delete-node/ core1;
                /delete-node/ core2;
                /delete-node/ core3;
            };
        };
        /delete-node/ cpu@1;
        /delete-node/ cpu@2;
        /delete-node/ cpu@3;
    };
    aliases {
        serial2 = &main_uart0; 
        serial3 = &main_uart5;
        mmc0 = &sdhci0;
        mmc1 = &sdhci1;
        mmc2 = &sdhci2;
        spi0 = &ospi0;
        ethernet0 = &cpsw_port1;
        ethernet1 = &cpsw_port2;
        usb0 = &usb0;
        usb1 = &usb1;
    };

    chosen {
        stdout-path = "serial2:460800n8";
        bootargs = "console=ttyS2,460800n8 earlycon=ns16550a,mmio32,0x02800000";
    };

    /*described regulators*/
    vbat: regulator-0 {
        compatible = "regulator-fixed";
        regulator-name = "vbat";
        regulator-min-microvolt = <7200000>;//ak809 7.2 input voltage
        regulator-max-microvolt = <7200000>;
        regulator-always-on;
        regulator-boot-on;
    };

    vdd_3v3:regulator-1 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_3v3";
        regulator-min-microvolt = <3300000>;
        regulator-max-microvolt = <3300000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_1v1:regulator-2 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_1v1";
        regulator-min-microvolt = <1100000>;
        regulator-max-microvolt = <1100000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_1v8:regulator-3 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_1v8";
        regulator-min-microvolt = <1800000>;
        regulator-max-microvolt = <1800000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };   
    vdd_5v:regulator-4 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_5v";
        regulator-min-microvolt = <5000000>;
        regulator-max-microvolt = <5000000>;
        vin-supply = <&vbat>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vdd_arm_0P85V:regulator-5 {
        compatible = "regulator-fixed";
        regulator-name = "vdd_arm_0P85V";
        regulator-min-microvolt = <850000>;
        regulator-max-microvolt = <850000>;
        vin-supply = <&vbat>;
        regulator-always-on;
        regulator-boot-on;        
    };

    vcc_3V0A:regulator-6 {
        compatible = "regulator-fixed";
        regulator-name = "vcc_3V0A";
        regulator-min-microvolt = <3000000>;
        regulator-max-microvolt = <3000000>;
        vin-supply = <&vdd_3v3>;
        regulator-always-on;
        regulator-boot-on;        
    };   

    tcxo_3v0:regulator-7 {
        compatible = "regulator-fixed";
        regulator-name = "tcxo_3v0";
        regulator-min-microvolt = <3000000>;
        regulator-max-microvolt = <3000000>;
        vin-supply = <&vdd_3v3>;
        regulator-always-on;
        regulator-boot-on;        
    };     

    rt_5v0:regulator-8 {
        compatible = "regulator-fixed";
        regulator-name = "rt_5v0";
        regulator-min-microvolt = <5000000>;
        regulator-max-microvolt = <5000000>;
        vin-supply = <&vdd_5v>;
        regulator-always-on;
        regulator-boot-on;        
    };   


    memory@80000000 {
        device_type = "memory";
        /*256M*/
        reg = <0x00000000 0x80000000 0x00000000 0x10000000>;

    };

    reserved-memory {
        #address-cells = <2>;
        #size-cells = <2>;
        ranges;

        ramoops@8c700000 {
            compatible = "ramoops";
            reg = <0x00 0x8c700000 0x00 0x00100000>;
            record-size = <0x8000>;
            console-size = <0x8000>;
            ftrace-size = <0x00>;
            pmsg-size = <0x8000>;
        };

        /* global cma region */
        linux,cma {
            compatible = "shared-dma-pool";
            reusable;
            size = <0x00 0x800000>;
            linux,cma-default;
        };

        rtos_ipc_memory_region: ipc-memories@8c800000 {
            compatible = "shared-dma-pool";
            reg = <0x00 0x8c800000 0x00 0x00300000>;
            no-map;
        };

        mcu_m4fss_dma_memory_region: m4f-dma-memory@8cb00000 {
            compatible = "shared-dma-pool";
            reg = <0x00 0x8cb00000 0x00 0x100000>;
            no-map;
        };

        mcu_m4fss_memory_region: m4f-memory@8cc00000 {
            compatible = "shared-dma-pool";
            reg = <0x00 0x8cc00000 0x00 0xe00000>;
            no-map;
        };

        wkup_r5fss0_core0_dma_memory_region: r5f-dma-memory@8da00000 {
            compatible = "shared-dma-pool";
            reg = <0x00 0x8da00000 0x00 0x00100000>;
            no-map;
        };

        wkup_r5fss0_core0_memory_region: r5f-memory@8db00000 {
            compatible = "shared-dma-pool";
            reg = <0x00 0x8db00000 0x00 0x00c80000>;
            no-map;
        };

        secure_tfa_ddr: tfa@8e780000 {
            reg = <0x00 0x8e780000 0x00 0x80000>;
            alignment = <0x1000>;
            no-map;
        };

        secure_ddr: optee@8e800000 {
            reg = <0x00 0x8e800000 0x00 0x00800000>; /* for OP-TEE */
            alignment = <0x1000>;
            no-map;
        };

        framebuffer: framebuffer@8f000000 {
            reg = <0x00 0x8f000000 0x00 0x800000>;
            no-map;
        };
    };

    leds:leds {
        status = "okay";
        compatible = "gpio-leds";

        LED-G {
                label = "led-g";
                gpios = <&main_gpio0 67 GPIO_ACTIVE_HIGH>;
                linux,default-trigger = "none";
                function = LED_FUNCTION_HEARTBEAT;
                default-state = "off";
            };
        LED-R {
            label = "led-r";
            gpios = <&main_gpio0 42 GPIO_ACTIVE_HIGH>;
            linux,default-trigger = "none";
            function = LED_FUNCTION_HEARTBEAT;
            default-state = "off";
        };    

        SD-PWR {
            label = "sd-pwr";
            gpios = <&main_gpio0 63 GPIO_ACTIVE_LOW>;
            default-state = "on";
        };

        KEY_LIGHT {
            status = "okay";
            label = "key_light";
            gpios = <&main_gpio1 38 GPIO_ACTIVE_HIGH>;
            default-state = "off";
        };

        GPS_ON {
            label = "gps_on";
            gpios = <&main_gpio0 36 GPIO_ACTIVE_LOW>;
            default-state = "off";
        };

        BT_ON {
            label = "bt_on";
            gpios = <&main_gpio0 37 GPIO_ACTIVE_LOW>;
            default-state = "off";
        };

        AUDIO_PA{
            label = "audio_pa";
            gpios = <&main_gpio0 57 GPIO_ACTIVE_HIGH>;
            default-state = "off";
        };

        // LCD_LIGHT {
        //     label = "lcd_light";
        //     gpios = <&main_gpio1 9 GPIO_ACTIVE_HIGH>;
        //     default-state = "off";
        // };        
    };

    gc9307_backlight: backlight {
        compatible = "pwm-backlight";
        pwms = <&epwm1 0 50000 0>;
        brightness-levels = <0 3 5 8 10 13 15 18 20 23 
                            26 28 31 33 36 38 41 43 46 
                            48 51 54 56 59 61 64 66 69 
                            71 74 76 79 82 84 87 89 92 
                            94 97 99 102 105 107 110 112 
                            115 117 120 122 125 127 130 133 
                            135 138 140 143 145 148 150 153 
                            156 158 161 163 166 168 171 173 
                            176 178 181 184 186 189 191 194 
                            196 199 201 204 207 209 212 214 
                            217 219 222 224 227 229 232 235 
                            237 240 242 245 247 250 252 255>;
        default-brightness-level = <50>;
    };

    matrix_keyboard: matrix-keyboard {
            status = "okay";
            compatible = "gpio-matrix-keypad"; 

            row-gpios=<
                &main_gpio0 45 GPIO_ACTIVE_LOW
                &main_gpio0 46 GPIO_ACTIVE_LOW
                &main_gpio0 47 GPIO_ACTIVE_LOW
                &main_gpio0 48 GPIO_ACTIVE_LOW
                &main_gpio0 49 GPIO_ACTIVE_LOW
            >;
            col-gpios=<
                &main_gpio0 50 GPIO_ACTIVE_LOW
                &main_gpio0 51 GPIO_ACTIVE_LOW
                &main_gpio0 52 GPIO_ACTIVE_LOW
                &main_gpio0 53 GPIO_ACTIVE_LOW
                &main_gpio0 54 GPIO_ACTIVE_LOW        
            >;
            /*
            victel keyboard:
                        R0     R1     R2    R3    R4
                C0   LEFT  RIGHT   [SP]  PRO1
                C1     1      2      3   PRO2
                C2     4      5      6   PRO3
                C3     7      8      9   VOL+
                C4     *      0      #   VOL-
            --
            full keyboard:
                        R0     R1     R2    R3      R4
                C0   LEFT  RIGHT  [DES]  PRO1    UP
                C1     1      2      3   PRO2    DOWN 
                C2     4      5      6   PRO3    CANCAL
                C3     7      8      9   VOL+    SURE
                C4     *      0      #   VOL-    CALL
            */    
            /*#define MATRIX_KEY(row, col, code)*/
            linux,keymap=<
                MATRIX_KEY(0,0,KEY_LEFT)  /*第0行，第0列的键的键码为KEY_LEFT*/
                MATRIX_KEY(1,0,KEY_RIGHT)
                MATRIX_KEY(2,0,KEY_SPACE)
                MATRIX_KEY(3,0,KEY_PROG1)
                MATRIX_KEY(4,0,KEY_UP)

                MATRIX_KEY(0,1,KEY_1)
                MATRIX_KEY(1,1,KEY_2)
                MATRIX_KEY(2,1,KEY_3)
                MATRIX_KEY(3,1,KEY_PROG2)
                MATRIX_KEY(4,1,KEY_DOWN)

                MATRIX_KEY(0,2,KEY_4)
                MATRIX_KEY(1,2,KEY_5)
                MATRIX_KEY(2,2,KEY_6)
                MATRIX_KEY(3,2,KEY_PROG3)
                MATRIX_KEY(4,2,KEY_HANGUP_PHONE)

                MATRIX_KEY(0,3,KEY_7)
                MATRIX_KEY(1,3,KEY_8)
                MATRIX_KEY(2,3,KEY_9)
                MATRIX_KEY(3,3,KEY_VOLUMEUP)
                MATRIX_KEY(4,3,KEY_ENTER)

                MATRIX_KEY(0,4,KEY_KPASTERISK)
                MATRIX_KEY(1,4,KEY_0)
                MATRIX_KEY(2,4,KEY_SLASH)
                MATRIX_KEY(3,4,KEY_VOLUMEDOWN)
                MATRIX_KEY(4,4,KEY_PICKUP_PHONE)

            >;
            gpio-activelow;
            debounce-delay-ms=<20>;
            col-scan-delay-us=<20>;
            linux,no-autorepeat;
    };

    user_keys: user-keys {
        status = "okay";
        compatible = "gpio-keys";

        button-alarm {
            label = "button-alarm";
            linux,code = <KEY_PHONE>;
            gpios = <&main_gpio0 86 GPIO_ACTIVE_LOW>;
        };

        button-middle {
            label = "button-middle";
            linux,code = <KEY_ENTER>;
            gpios = <&main_gpio0 61 GPIO_ACTIVE_LOW>;
        };        

        button-ptt {
            label = "button-ptt";
            linux,code = <KEY_CHAT>;
            gpios = <&main_gpio0 85 GPIO_ACTIVE_LOW>;
        };        

        /*earphone detect,active low*/
        button-id {
            label = "button-id";
            linux,code = <KEY_MEDIA>;
            gpios = <&main_gpio1 7 GPIO_ACTIVE_LOW>;
        };            
    };

    knob_keys: knob-keys {
        status = "okay";
        compatible = "gpio-keys";

        encoder {
            label = "encoder";
            linux,code = <KEY_MUTE>;
            gpios = <&main_gpio1 1 GPIO_ACTIVE_LOW>;
			interrupts-extended = <&main_gpio1 1 IRQ_TYPE_EDGE_RISING>,
				<&main_pmx0 0x17c>;
			interrupt-names = "irq", "wakeup";
        };

        encoder-right {
            label = "encoder-right";
            linux,code = <KEY_VOLUMEUP>;
            gpios = <&main_gpio1 3 GPIO_ACTIVE_LOW>;
        };

        encoder-left {
            label = "encoder-left";
            linux,code = <KEY_VOLUMEDOWN>;
            gpios = <&main_gpio1 6 GPIO_ACTIVE_LOW>;
        };
    };

    main_pwm0: dmtimer-main-pwm-0 {
		pinctrl-0 = <&timer19p2mhz_pins_default>;
		pinctrl-names = "default";
		compatible = "ti,am62x-dmtimer";
		// #pwm-cells = <3>;
		ti,timers = <&main_timer1>;
        ti,clock-source = <0x0>;
        // ti,prescaler = <0x0>;
	};

    pps {
		compatible = "pps-gpio";
		pinctrl-names = "default";
		pinctrl-0 = <&timerpps_pins_default>;
		gpios = <&main_gpio1 30 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

    dac124s085: dac_card {
        compatible = "victel,dac124s085";
        #sound-dai-cells = <0>;
    };

    /delete-node/ mclk;
    mclk: oscillator {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <********>; /*<********>; *********/
        clock-output-names = "mclk";
    };

    timer_ext_ck: timer_ext_ck {
        compatible = "fixed-clock";
        #clock-cells = <0>;
        clock-frequency = <********>; /*<********>; *********/
        clock-output-names = "timer_ext_ck";
    };  

    mcasp0_codec {
        compatible = "simple-audio-card";
        simple-audio-card,name = "mcasp0voice";
        #address-cells = <1>;
        #size-cells = <0>;

        simple-audio-card,format = "i2s";
        simple-audio-card,widgets =
            "Microphone", "Microphone Jack",
            "Speaker",    "External Speaker";
            
        simple-audio-card,routing =
            /*Sink , Source*/
            "External Speaker",    "JL7016G OUTPUT",/* SPK*/
            "JL7016G INPUT",       "Microphone Jack"; /*MIC_IN+*/


        simple-audio-card,dai-link@0 {
            reg = <0>;
            format = "i2s";
            bitclock-master = <&sound0_0_master>;
            frame-master = <&sound0_0_master>;
            cpu {
                sound-dai = <&mcasp0>;
            };

            sound0_0_master: codec {
                sound-dai = <&jlcodec>;
            };
        };
    };
	mcasp2_codec {
        compatible = "simple-audio-card";
        #address-cells = <1>;
        #size-cells = <0>;
        simple-audio-card,name = "mcasp2c";

        /*定义机器驱动部件，字符串成对出现，
         *部件左边时simplecard定义的部件类型，
         *右边定义的为部件名称
         *部件类型：Microphone，Line，Headphone，Speaker
         *部件名称在routing 属性中使用
         */
        simple-audio-card,widgets =
            "Line","Line In",
            "Line","Line Out";

        /*成对出现，
         *左边:sink，右边:sources
         *声卡中设备"simple-audio-card,widgets"部件和codec驱动部件关联
         */
        simple-audio-card,routing =
            "Line Out", "DA_SDO",
            "AD_SDI",   "Line In"; 

        /*tx-path only playback*/
        simple-audio-card,dai-link@0 {
            reg = <0>;
            format = "dsp_a";
            bitclock-master = <&ak2401a1st_placyback_clock>;
            frame-master = <&ak2401a1st_placyback_clock>;
            ak2401a1st_placyback_clock: cpu {
                sound-dai = <&mcasp2>;
                system-clock-direction-out;
            };
            codec {
                sound-dai = <&ak2401a1st 0>;
            };
        };

        /*rx-path only capture*/ 
        simple-audio-card,dai-link@1 {
            reg = <1>;
            format = "i2s";
            bitclock-master = <&ak2401a1st_capture_clock>;
            frame-master = <&ak2401a1st_capture_clock>;
            /*frame-inversion;*/
            cpu {
                sound-dai = <&mcasp2>;
            };
            ak2401a1st_capture_clock: codec {
                sound-dai = <&ak2401a1st 1>;
                clocks = <&mclk>;
            };
        };                 
            
	};
	mcasp1_codec {
        compatible = "simple-audio-card";
        #address-cells = <1>;
        #size-cells = <0>;
        simple-audio-card,name = "mcasp1c";

        /*定义机器驱动部件，字符串成对出现，
         *部件左边时simplecard定义的部件类型，
         *右边定义的为部件名称
         *部件类型：Microphone，Line，Headphone，Speaker
         *部件名称在routing 属性中使用
         */
        simple-audio-card,widgets =
            "Line","Line In",
            "Line","Line Out";

        /*成对出现，
         *左边:sink，右边:sources
         *声卡中设备"simple-audio-card,widgets"部件和codec驱动部件关联
         */
        simple-audio-card,routing =
            "Line Out", "DAC124S085 Output",
            "AD_SDI",   "Line In"; 

        simple-audio-card,dai-link@0 {
            reg = <0>;
            format = "dsp_a";
            bitclock-master = <&ak2401a2nd_playback_clock>;
            frame-master = <&ak2401a2nd_playback_clock>;
            ak2401a2nd_playback_clock:cpu {
                sound-dai = <&mcasp1>;
                system-clock-direction-out;
            };
            codec {
                sound-dai = <&dac124s085>;
            };
        };

        simple-audio-card,dai-link@1 {
            reg = <1>;
            format = "i2s";
            bitclock-master = <&ak2401a2nd_capture_clock>;
            frame-master = <&ak2401a2nd_capture_clock>;
            //frame-inversion;
            //bitclock-inversion;
            cpu {
                sound-dai = <&mcasp1>;
                //system-clock-direction-out;
            };
            ak2401a2nd_capture_clock: codec {
                sound-dai = <&ak2401a2nd 0>;
                clocks = <&mclk>;
            };            
        };       
	};  

    timeslot: ts {
        status = "okay";
        compatible = "victel,timeslot";
        ti,clock-source = <0x02>; /* 00 - timer_sys_ck 1 - 32k 2 - ext-refclk1 */
        ti,timers = <&main_timer0>;
        clocks = <&mclk>;
        clock-names = "fck";
        clk-sel-reg = <0x001081b0>;/*14.2.1.1.1.134.1 CFG0_CLKSEL Register (Offset = 81B0h) [reset = 0h ]*/
    };  

    rfcontrol: rfdev {
        status = "okay";
        compatible = "victel,rfcontrol";
        rfpwr-gpio    = <&mcu_gpio0   17 GPIO_ACTIVE_HIGH>;
        txon-gpio     = <&mcu_gpio0   18 GPIO_ACTIVE_HIGH>;
        rxon-gpio     = <&mcu_gpio0   19 GPIO_ACTIVE_HIGH>;
        rxon2-gpio    = <&main_gpio0  76 GPIO_ACTIVE_HIGH>;
        rxvco-gpio    = <&mcu_gpio0   20 GPIO_ACTIVE_HIGH>;
        rxvco2-gpio   = <&main_gpio0  75 GPIO_ACTIVE_HIGH>;
        psapc-gpio    = <&mcu_gpio0  23 GPIO_ACTIVE_HIGH>;
        main-pll = "ak2401";
        aux-pll = "ak2401";
    };

    gpio_poweroff: gpio-poweroff {
        compatible = "gpio-poweroff";
        gpios = <&main_gpio0 60 GPIO_ACTIVE_LOW>;
    };
};

/*统一地方对GPIO管脚进行复用*/
&main_gpio0 {
    pinctrl-names = "default";
    pinctrl-0 = <&gpio0_pins_default>;
    gpio-line-names = 
        "", "", "", "", "",   /* 0 - 4*/
        "", "", "", "", "",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "", "", "",   /* 15 - 19*/
        "", "", "", "", "",   /* 20 - 24*/
        "", "", "", "", "",   /* 25 - 29*/
        "", "", "", "", "",   /* 30 - 34*/
        "", "GPS_ON", "BT_ON_N", "", "BT_STATE",   /* 35 - 39*/
        "", "", "LED_R", "", "",   /* 40 - 44*/
        "KEY_R0", "KEY_R1", "KEY_R2", "KEY_R3", "KEY_R4",   /* 45 - 49*/
        "KEY_C0", "KEY_C1", "KEY_C2", "KEY_C3", "KEY_C4",   /* 50 - 54*/
        "", "", "AUDIO_PA_ON", "", "CODEC2_RST_N",   /* 55 - 59*/
        "PWR_OFF", "KEY_MIDDLE", "LCD_RS", "SD_EN_N", "NFC_PD",   /* 60 - 64*/
        "", "", "LED_G", "", "",   /* 65 - 69*/
        "", "", "", "SIM_CLK", "SIM_IO",   /* 70 - 74*/
        "IO1_JM", "IO2_JM", "IO3_JM", "IO4_JM", "JM_EN_N",   /* 75 - 79*/
        "SIM_EN_N", "", "", "", "",   /* 80 - 84*/
        "PTT_N", "ALARM_N", "", "", "",   /* 85 - 89*/
        "", "";              /* 90 - 91*/
};

&main_gpio1 {
    pinctrl-names = "default";
    pinctrl-0 = <&gpio1_pins_default>;
    gpio-line-names = 
        "", "ENC_S", "", "ENC_A", "",   /* 0 - 4*/
        "", "ENC_B", "ID", "", "LCD_LIGHT",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "", "", "",   /* 15 - 19*/
        "", "", "", "", "",   /* 20 - 24*/
        "", "", "", "", "",   /* 25 - 29*/
        "", "", "", "", "",   /* 30 - 34*/
        "", "", "", "KEY_LIGHT", "GP1_39",   /* 35 - 39*/
        "", "", "", "", "",   /* 40 - 44*/
        "", "", "", "", "",   /* 45 - 49*/
        "", "";               /* 50 - 51*/
};

&mcu_gpio0 {
    pinctrl-names = "default";
    pinctrl-0 = <&mcugpio0_pins_default>;
    gpio-line-names = 
        "", "", "", "", "",   /* 0 - 4*/
        "", "", "", "", "",   /* 5 - 9*/
        "", "", "", "", "",   /* 10 - 14*/
        "", "", "RF_PWR_EN", "TX_ON", "RX_ON",   /* 15 - 19*/
        "RX_VCO_ON", "", "RX_PLL_LD", "PS_APC";      /* 20 - 23*/
};

&epwm1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&epwm1_pins_default>;
};

&main_spi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi0lcdadc_pins_default>;
    // memory-region = <&framebuffer>;
    dmas = <&main_pktdma 0xc300 0>, <&main_pktdma 0x4300 0>;
    dma-names = "tx0", "rx0";
    ti,spi-num-cs = <4>;
    ti,pindir-d0-out-d1-in;

    panel: display@0 {
            compatible = "victel,gc9307", "jianghehai,xhzy1713268za1";
            spi-max-frequency = <50000000>;
            reg = <0>;
            rgb;
            buswidth = <8>;
            rotate = <180>;
            reset-gpio = <&main_gpio0 32 GPIO_ACTIVE_LOW>; 
            dc-gpio = <&main_gpio0 35 GPIO_ACTIVE_HIGH>;
            cs-gpio = <&main_gpio1 15 GPIO_ACTIVE_LOW>;
            led-gpio = <&main_gpio1 9 GPIO_ACTIVE_HIGH>;
            backlight = <&gc9307_backlight>;
    };

    spc795x: spc795x_adc@1 {
        compatible = "samso,spc7951";
        reg = <1>;
        #io-channel-cells = <1>;
        spi-max-frequency = <1000000>;
        ti,spi-wdelay = <63>;
    };
};

&wkup_uart0 {
    /* WKUP UART0 is used by DM firmware */
    status = "reserved";
};

&main_uart0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&uart0debug_pins_default>;
    interrupts-extended = <&gic500 GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>,
                   <&main_pmx0 0x1c8>; /* (D14) UART0_RXD PADCONFIG114 */
    interrupt-names = "irq", "wakeup";
    dmas = <&main_pktdma 0xc400 0>, <&main_pktdma 0x4400 0>;
    dma-names = "tx", "rx";
};

&main_uart5 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&uart5gps_pins_default>;
};

&sdhci1 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&mmc1sd_pins_default>;
    ti,driver-strength-ohm = <50>;
    bus-width = <4>;
    disable-wp;
    sdhci-caps-mask = <0x00000007 0x00200000>;
    max-frequency = <25000000>;
    no-1-8-v;
    non-removable;
    broken-cd;
    ti,fails-without-test-cd;
    cap-sd-highspeed;
};



&mailbox0_cluster0 {
    mbox_m4_0: mbox-m4-0 {
        ti,mbox-rx = <0 0 0>;
        ti,mbox-tx = <1 0 0>;
    };

    mbox_r5_0: mbox-r5-0 {
        ti,mbox-rx = <2 0 0>;
        ti,mbox-tx = <3 0 0>;
    };
};

&mcu_m4fss {
    mboxes = <&mailbox0_cluster0 &mbox_m4_0>;
    memory-region = <&mcu_m4fss_dma_memory_region>,
            <&mcu_m4fss_memory_region>;
};

&wkup_r5fss0_core0 {
    mboxes = <&mailbox0_cluster0 &mbox_r5_0>;
    memory-region = <&wkup_r5fss0_core0_dma_memory_region>,
            <&wkup_r5fss0_core0_memory_region>;
};

&ospi0 {
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&qspi0flash_pins_default>;
    flash@0{
        compatible = "is25lp512", "jedec,spi-nor";
        reg = <0x0>;
        spi-tx-bus-width = <4>;
        spi-rx-bus-width = <4>;
        spi-max-frequency = <25000000>;
        cdns,tshsl-ns = <60>;
        cdns,tsd2d-ns = <60>;
        cdns,tchsh-ns = <60>;
        cdns,tslch-ns = <60>;
        cdns,read-delay = <0>;
        cdns,phy-mode;

        partitions {
            compatible = "fixed-partitions";
            #address-cells = <1>;
            #size-cells = <1>;

            partition@0 {
                label = "ospi.tiboot3";
                reg = <0x0 0x80000>;
            };

            partition@80000 {
                label = "ospi.tispl";
                reg = <0x80000 0x100000>;
            };

            partition@180000 {
                label = "ospi.u-boot";
                reg = <0x180000 0x100000>;
            };

            partition@280000 {
                label = "ospi.dtb";
                reg = <0x280000 0x20000>;
            };
            
            partition@2a0000 {
                label = "ospi.prod";
                reg = <0x2a0000 0x20000>;
            };   

            partition@2c0000 {
                label = "ospi.calibration";
                reg = <0x2c0000 0x40000>;
            };

            partition@300000 {
                label = "ospi.kernel";
                reg = <0x300000 0x800000>;
            };

            partition@b00000 {
                label = "rootfs";
                reg = <0xb00000 0x1500000>;
            };

            partition@2000000 {
                label = "victel";
                reg = <0x2000000 0x1fc0000>;
            };
            /* 16MB --  fc0000
             * 32MB -- 1fc0000
             * 64MB -- 3fc0000
             */
            partition@3fc0000 {
                label = "ospi.phypattern";
                reg = <0x3fc0000 0x40000>;
            };
        };
    };
}; 

/*cs 0 -- SKY72310，cs1 4819 cs2 ak2401a*/
&mcu_spi0{
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&mcuspi0pll481972310_pins_default>;
    #address-cells = <1>;
    #size-cells = <0>;
    ti,spi-num-cs = <4>;
    ti,pindir-d0-out-d1-in;
    SKY72310@0{
        compatible = "victel,sky72310";
        spi-max-frequency = <24000000>;
        label = "sky72310";
        ld-gpios    = <&mcu_gpio0 22 GPIO_ACTIVE_HIGH>;
        reg = <0>;
    };     
    ak2401a2nd: ak2401a2nd@2{
        compatible = "victel,ak2401a";
        #sound-dai-cells = <1>;
        spi-max-frequency = <24000000>;
        label = "ak2401a";
        reg = <2>;
        ld-gpio    = <&main_gpio0 29 GPIO_ACTIVE_HIGH>;
        rst-gpio   = <&main_gpio0 26 GPIO_ACTIVE_HIGH>;
        rxpdn-gpio = <&main_gpio0 27 GPIO_ACTIVE_HIGH>;
        txpdn-gpio = <&main_gpio0 28 GPIO_ACTIVE_HIGH>;
        agc-gpio   = <&main_gpio0 30 GPIO_ACTIVE_HIGH>; 
    };        
};

&main_spi1{
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&spi1ad9864_pins_default>;
    #address-cells = <1>;
    #size-cells = <0>;
    ti,spi-num-cs = <4>;
    ti,pindir-d0-out-d1-in;
    AD9864@0 {
        compatible = "victel,ad9864";

        spi-max-frequency = <24000000>;
        reg = <0>;
    };  
    
    ak2401a1st: ak2401a1st@2{
        compatible = "victel,ak2401a";
        #sound-dai-cells = <1>;
        spi-max-frequency = <24000000>;
        reg = <2>;  
        ld-gpio    = <&main_gpio0 55 GPIO_ACTIVE_HIGH>;
        rst-gpio   = <&main_gpio0 78 GPIO_ACTIVE_HIGH>;
        rxpdn-gpio = <&main_gpio0 79 GPIO_ACTIVE_HIGH>;
        txpdn-gpio = <&main_gpio0 80 GPIO_ACTIVE_HIGH>;
        agc-gpio   = <&main_gpio0 56 GPIO_ACTIVE_HIGH>;     
        config-path= <1>; /*0 - spi, 1 - mcasp*/       
    };    
};
&mcasp0 {
    status = "okay";
    #sound-dai-cells = <0>;

    pinctrl-names = "default";
    pinctrl-0 = <&mcasp0codec_pins_default>;

    op-mode = <0>;          /* MCASP_IIS_MODE */
    tdm-slots = <2>;

    /*
     * CODEC_DIN  <-- MCASP0_AXR0
     * CODEC_DOUT --> MCASP0_AXR2
     */
    serial-dir = <  /* 0: INACTIVE, 1: TX, 2: RX */
           1 0 2 0
           0 0 0 0
           0 0 0 0
           0 0 0 0
    >;

    /*配置收发FIFO，没有或者为0 禁止FIFO*/
    tx-num-evt = <1>;
    rx-num-evt = <1>;

    /*1 - i2s,4-dspa*/
    tx-format = <1>;
    rx-format = <1>;

    /*SND_SOC_DAIFMT_BC_FC-4  SND_SOC_DAIFMT_BP_FP - 1*/
    tx-clk-provider = <4>;
    rx-clk-provider = <4>;
};

/*AD964 DATA MCASP Bus*/
&mcasp1{
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&mcasp1txdac_pins_default>;
    #sound-dai-cells = <0>;
    op-mode = <0>;          /* 0 - I2S or 1 - DIT operation mode */
    tdm-slots = <2>;
    sys-ctr-clk-sel-reg = <0x00108354>;
    audio_clock_src="AUDIO_EXT_REFCLK1";
    clock-frequency = <********>;

    /*AD_SDO2  --> MCASP1_AXR0
     *DAC_DATA <-- MCASPXR1
     */
    serial-dir = <  /* 0: INACTIVE, 1: TX, 2: RX */
           2 1 0 0
           0 0 0 0
           0 0 0 0
           0 0 0 0
    >;
    tx-num-evt = <0>;
    rx-num-evt = <0>;    

    /*1 - i2s,4-dspa*/
    tx-format = <4>;
    rx-format = <1>;

    /*SND_SOC_DAIFMT_BC_FC-4  SND_SOC_DAIFMT_BP_FP - 1*/
    tx-clk-provider = <1>;
    rx-clk-provider = <4>;

    /*fsx need pull high after stop transfer*/
    fsx-pull-high=<1>;
};
&mcasp2{
    status = "okay";
    pinctrl-names = "default";
    pinctrl-0 = <&mcasp2ad9864_pins_default>;
    #sound-dai-cells = <0>;
    op-mode = <0>;    /* 0 - I2S or 1 - DIT operation mode */
    tdm-slots = <2>;
    clk-sel-reg = <0x00108358>;
    audio_clock_src="AUDIO_EXT_REFCLK1";
    clock-frequency = <********>;
    /*AD_SDO1 -->MCASP2_AXR0
     *DA_SDI1 <--MCASP2_AXR1
     */
    serial-dir = <  /* 0: INACTIVE, 1: TX, 2: RX */
           2 1 0 0
           0 0 0 0
           0 0 0 0
           0 0 0 0
    >;
    tx-num-evt = <0>;
    rx-num-evt = <0>;    
    /*1 - i2s,4-dspa*/
    tx-format = <4>;
    rx-format = <1>;

    /*SND_SOC_DAIFMT_BC_FC-4  SND_SOC_DAIFMT_BP_FP - 1*/
    tx-clk-provider = <1>;
    rx-clk-provider = <4>;

    /*fsx need pull high after stop transfer*/
    fsx-pull-high=<1>;
};


/*
 * All SoC variants with the AMC package have no PRU.
 * Attempting to access the PRU on these devices will
 * result in a crash at kernel bootup.
 *
 * For now, we do not have any code that can figure out
 * the absence of the PRU by reading any SoC registers,
 * so for now disable the PRU here in the board DTS file.
 */
&pruss {
        status = "disabled";
};

&main_i2c0 {
    status = "okay";
    pinctrl-names = "default";    
    pinctrl-0 = <&codeci2c_pins_default>;
    clock-frequency = <400000>;
    status = "okay";    

    tlv320aic3204: tlv320aic3204@18 {
        #sound-dai-cells = <0>;
        compatible = "ti,tlv320aic32x4";
        reg = <0x18>;
        ldoin-supply = <&vdd_3v3>;
        iov-supply = <&vdd_3v3>;
        clocks = <&mclk>;
        clock-names = "mclk";

        status = "okay";
        reset-gpios = <&main_gpio0 59 GPIO_ACTIVE_LOW>;
    };    

    sa6100lp: sa6100lp@12 {
        compatible = "victel,sa6100lp";
        reg = <0x12>;
        status = "okay";
    };

    bh1730fvc: bh1730fvc@29 {
        compatible = "victel,bh1730fvc";
        reg = <0x29>;
        status = "okay";
    };

    sc5883lp: sc5883lp@2c {
        compatible = "victel,sc5883lp";
        reg = <0x2c>;
        status = "okay";
    };

    tpl0401a: tpl0401a@2e {
        compatible = "tpl0401-103";
        reg = <0x2e>;
        status = "okay";
    };  
    jlcodec: jlcodec@5c{
        #sound-dai-cells = <0>;
        compatible = "victel,jl7016g";
        reg = <0x5c>;
        status = "okay";
    };
};


