# GC9307 U-Boot 显示驱动调试指南

## 概述

本指南详细说明如何为 GC9307 显示驱动启用调试输出，以及如何排查驱动初始化和运行问题。

## 1. 调试配置选项

### 1.1 启用全局调试支持

在 U-Boot 配置中启用以下选项：

```bash
# 在 defconfig 文件中添加或确保存在以下配置
CONFIG_LOG=y
CONFIG_LOG_MAX_LEVEL=7
CONFIG_LOG_DEFAULT_LEVEL=6
CONFIG_DEBUG_UART=y
CONFIG_SPL_LOG=y
CONFIG_SPL_LOG_MAX_LEVEL=7
```

### 1.2 编译时启用调试

有三种方法启用调试输出：

#### 方法1：在源文件中启用（已实现）
在 `gc9307_uboot.c` 文件顶部已添加：
```c
#define DEBUG
#define LOG_CATEGORY LOGC_VIDEO
```

#### 方法2：编译时定义
```bash
make KCFLAGS="-DDEBUG" 
```

#### 方法3：针对特定文件启用
```bash
# 在 Makefile 中为特定文件启用调试
CFLAGS_gc9307_uboot.o := -DDEBUG
```

## 2. 运行时调试配置

### 2.1 设置日志级别

在 U-Boot 命令行中：
```bash
# 设置全局日志级别为调试级别
setenv loglevel 7

# 设置视频子系统日志级别
log level video 7

# 查看当前日志配置
log rec
```

### 2.2 启用特定类别的调试

```bash
# 启用视频相关调试
log level video debug

# 启用 SPI 相关调试
log level spi debug

# 启用 GPIO 相关调试  
log level gpio debug
```

## 3. 调试输出内容

### 3.1 驱动初始化调试信息

启用调试后，您将看到以下类型的输出：

```
GC9307: Starting probe function
GC9307: SPI device assigned
GC9307: Requesting GPIO pins
GC9307: Reset GPIO acquired
GC9307: DC GPIO acquired
GC9307: CS GPIO acquired
GC9307: LED GPIO acquired
GC9307: Display properties: 320x240, rotation=0
GC9307: Video properties configured
GC9307: Enabling backlight
GC9307: Backlight enabled
GC9307: Starting display initialization sequence
GC9307: Starting display initialization
GC9307: Performing hardware reset sequence
GC9307: Hardware reset completed
GC9307: Sending software reset command
GC9307: Writing command 0x01
GC9307: Command 0x01 sent successfully
```

### 3.2 SPI 通信调试信息

```
GC9307: Writing command 0x11
GC9307: Command 0x11 sent successfully
GC9307: Writing data 0x05
GC9307: Data 0x05 sent successfully
GC9307: Setting address window: x1=0, y1=0, x2=319, y2=239
GC9307: Address window set successfully
```

### 3.3 错误调试信息

```
GC9307: Failed to set DC GPIO low for command 0x01: -22
GC9307: SPI command 0x01 transfer failed: -110
GC9307: Failed to claim SPI bus: -16
```

## 4. 查看调试输出的方法

### 4.1 串口输出

确保您的调试串口配置正确：
```bash
# 检查串口配置
printenv baudrate
printenv stderr
printenv stdout
```

### 4.2 使用 log 命令

```bash
# 显示最近的日志记录
log rec

# 显示特定级别的日志
log rec -l debug

# 显示特定类别的日志
log rec -c video
```

## 5. 常见调试场景

### 5.1 驱动未加载

检查设备树配置：
```bash
# 查看设备树中的显示设备
fdt list /soc/spi@20000000/gc9307@0
```

### 5.2 GPIO 问题

```bash
# 检查 GPIO 状态
gpio status
gpio read <gpio_number>
```

### 5.3 SPI 通信问题

```bash
# 检查 SPI 总线
spi probe
```

## 6. 性能调试

### 6.1 启用时间戳

```bash
# 启用日志时间戳
log format-add timestamp
```

### 6.2 监控帧缓冲区更新

调试输出将显示：
```
GC9307: Starting video sync
GC9307: Framebuffer size: 153600 bytes
GC9307: SPI bus claimed
GC9307: Sending framebuffer data (153600 bytes)
GC9307: Video sync completed successfully
```

## 7. 调试技巧

### 7.1 分阶段调试

1. 首先确认驱动加载：查看 probe 函数输出
2. 检查 GPIO 初始化：确认所有 GPIO 获取成功
3. 验证 SPI 通信：查看命令和数据传输
4. 监控显示初始化：跟踪初始化序列
5. 检查帧缓冲区操作：监控 video_sync 调用

### 7.2 使用条件调试

可以在代码中添加条件调试：
```c
#ifdef CONFIG_VIDEO_GC9307_DEBUG
    debug("GC9307: Detailed debug info\n");
#endif
```

### 7.3 十六进制数据转储

对于 SPI 数据，可以添加十六进制转储：
```c
debug("GC9307: Data dump: ");
for (int i = 0; i < len && i < 16; i++) {
    debug("%02x ", buf[i]);
}
debug("\n");
```

## 8. 故障排除

### 8.1 无调试输出

1. 确认 `#define DEBUG` 在文件顶部
2. 检查 CONFIG_LOG 是否启用
3. 验证日志级别设置
4. 确认串口输出正常

### 8.2 输出过多

```bash
# 降低日志级别
log level video info

# 或者禁用特定调试
#undef DEBUG  // 在源文件中
```

### 8.3 性能影响

调试输出会影响性能，生产环境中应禁用：
```bash
# 编译时禁用调试
make KCFLAGS="-UDEBUG"
```

## 9. 高级调试功能

### 9.1 使用 log_debug 宏

```c
log_debug("GC9307: Advanced debug with category\n");
```

### 9.2 缓冲区内容检查

```c
log_buffer(LOG_CATEGORY, LOGL_DEBUG, 0, buffer, 1, 16, 16);
```

### 9.3 函数跟踪

```c
debug("%s: Entry\n", __func__);
debug("%s: Exit with ret=%d\n", __func__, ret);
```

这个调试系统将帮助您快速定位和解决 GC9307 显示驱动的问题。
